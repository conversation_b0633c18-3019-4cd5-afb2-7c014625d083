{"name": "@geon-map/react-odf", "version": "0.0.0", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"development": {"types": "./src/index.ts", "default": "./src/index.ts"}, "production": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs", "default": "./dist/index.js"}}}, "sideEffects": false, "files": ["src/**", "dist/**"], "scripts": {"build": "tsup", "dev": "tsup --watch", "type-check": "tsc --noEmit", "test": "vitest", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist", "lint": "eslint . --max-warnings 0"}, "dependencies": {"@geon-map/core": "workspace:*", "clsx": "^2.1.1", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@config/eslint": "workspace:*", "@config/typescript": "workspace:*", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/node": "^22.17.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "eslint": "^9.32.0", "react": "^19.1.1", "tsup": "^8.5.0", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "peerDependencies": {"react": "^19.0.0"}, "publishConfig": {"access": "restricted"}}